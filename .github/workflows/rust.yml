name: Rust

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main", "feature/*" ]

env:
  CARGO_TERM_COLOR: always

jobs:
  build:
    runs-on: ubuntu-latest
    steps:

      - uses: actions/checkout@v4

      - name: cargo build
        run: RUSTFLAGS=-Awarnings scripts/cargo_to_gh rustc --message-format json

      - name: check for any changes in the git tree
        run: |
          exit_code=0
          while read -r status_code file_path; do
            if [[ -z "$status_code" ]]; then
              continue
            fi
            exit_code=1
            echo "::error file=$file_path,title=git-status::<$status_code> $file_path"
          done <<<"$(git status --porcelain)"
          exit "$exit_code"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: target/
          retention-days: 1

  check:
    strategy:
      matrix:
        tool: [ check, clippy ]
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v4
      - name: cargo ${{ matrix.tool }}
        run: scripts/cargo_to_gh "$CHECK_TOOL"
        env:
          CHECK_TOOL: ${{ matrix.tool }}
      - name: cargo ${{ matrix.tool }} (no special formatting)
        if: failure()
        run: cargo "$CHECK_TOOL"
        env:
          CHECK_TOOL: ${{ matrix.tool }}

  test:
    runs-on: ubuntu-latest
    needs: build
    steps:

      - uses: actions/checkout@v4

      - name: cargo test
        run: |
          # convert e.g. "thread 'fmt_str::tests::text_html' panicked at src/fmt_str.rs:75:9:" to
          #              "::error file=src/fmt_str,line=75,col=9,title=test failure:: atfmt_str::tests::text_html"
          set -o pipefail
          cargo test --verbose | sed -E "s/thread '([^']+)' panicked at ([^:]+):([0-9]+):([0-9]+):$/::error file=\\2,line=\\3,col=\\4,title=test failure::at \\1/"

      - name: list ignored tests
        run: |
          (find . -name '*.rs' -exec grep --fixed-strings -Hno '#[ignore]' {} \; || true) | sed -E 's/^([^:]+):([^:]+):.*/::warning file=\1,line=\2,title=Ignored test::Regex indicates this test is probably ignored/'

      - name: check ignored tests all fail
        run: |
          exit_code=0
          while IFS= read -r line; do
            printf "::error title=Ignored test is passing::%s but expected failure because it's ignored"'\n' "$line"
            exit_code=1
          done < <(cargo test -- --ignored --color never | grep '\.\.\. ok$' || true)
          exit "$exit_code"

  fmt:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: cargo fmt
        run: cargo fmt --check

  vis-keywords:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4

      - name: check file
        run: file scripts/flatten-rustdoc-json

      - name: cargo docs (public only)
        run: cargo +nightly rustdoc --lib -- -Zunstable-options --output-format json

      - name: flatten rustdoc json (public only)
        run: scripts/flatten-rustdoc-json > target/items-public-only.txt

      - name: cargo docs (all)
        run: cargo +nightly rustdoc --lib -- -Zunstable-options --output-format json --document-private-items

      - name: flatten rustdoc json (all)
        run: IGNORE_ITEMS='enum mdq::query::pest::Rule' scripts/flatten-rustdoc-json > target/items-all.txt

      - name: compare
        run: |
          if diff -y --suppress-common-lines target/items-public-only.txt target/items-all.txt; then
            echo "::notice title=pub-vis::all pub items are actually public"
          else
            echo "::error title=pub-vis::some items marked pub are not actually public"
            exit 1
          fi

  help-text:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: target/

      - name: Generate help text
        run: cargo run -- --help > help.txt
      - name: Check help text is committed
        run: |
          if ! git diff --exit-code help.txt; then
            echo "::error title=help text::help.txt is out of date. Run 'cargo run -- --help > help.txt' and commit the result."
            exit 1
          fi
